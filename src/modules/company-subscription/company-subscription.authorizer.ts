import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { RoleTypeEnum } from '@constants';
import { CompanySubscriptionDto } from './dto/company-subscription.gql.dto';
import { getRepository } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import moment from 'moment';

@Injectable()
export class CompanySubscriptionAuthorizer implements Authorizer<CompanySubscriptionDto> {
  async authorize(context: any): Promise<Filter<CompanySubscriptionDto>> {
    try {
      // Admin authorizer - can access all subscriptions
      if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

      // User authorizer
      const userId = context?.req?.user?.id;
      if (!userId) throw new UnauthorizedException('User not found');

      const user = await getRepository(UserEntity).findOne({
        id: userId
      });

      if (!user) throw new UnauthorizedException('User not found');

      if (context?.req?.user?.type === RoleTypeEnum.User) {
        // For delete operations, check if user is company owner
        if (context?.operationName === 'deleteOne' || context?.operationName === 'deleteMany') {
          return this.authorizeDelete(userId, user.companyId);
        }

        // For other operations, allow access to company subscriptions
        return Promise.resolve({
          companyId: {
            eq: user.companyId
          },
          subscriptionEndDate: {
            gt: moment().subtract(10, 'days').toDate()
          }
        });
      }
    } catch (e) {
      throw new Error('Something went wrong in company subscription authorizer! authorize function');
    }
  }

  /**
   * Authorize delete operations - only company owners can delete subscriptions
   */
  private async authorizeDelete(userId: number, userCompanyId: number): Promise<Filter<CompanySubscriptionDto>> {
    try {
      // Get the company to check if user is the owner
      const company = await getRepository(CompanyEntity).findOne({
        where: { id: userCompanyId }
      });

      if (!company) {
        throw new UnauthorizedException('Company not found');
      }

      // Check if the user is the company owner
      if (company.ownerId !== userId) {
        throw new UnauthorizedException('Only company owners can cancel subscriptions');
      }

      // Return filter that allows access to company subscriptions
      return Promise.resolve({
        companyId: {
          eq: userCompanyId
        }
      });
    } catch (e) {
      throw new UnauthorizedException('Only company owners can cancel subscriptions');
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
