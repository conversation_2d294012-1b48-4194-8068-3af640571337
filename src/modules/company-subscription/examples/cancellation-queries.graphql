# Example GraphQL queries for subscription cancellation

# 1. Delete (Cancel) a subscription - Only company owners can do this
# This uses soft deletion (sets deletedAt field)
mutation CancelSubscription($id: ID!) {
  deleteOneCompanySubscription(input: { id: $id }) {
    id
    isCancelled
    cancellationType
    cancellationReason
    refundAmount
    deletedAt
    deletedBy
  }
}

# Variables for the above mutation:
# {
#   "id": "123"
# }

# 2. Query subscriptions with cancellation status
query GetCompanySubscriptionsWithStatus {
  companySubscriptions {
    id
    seatCount
    isYearly
    subscriptionEndDate
    nextBillingDate
    creditBalance
    
    # Cancellation fields
    isCancelled
    isActive
    isExpired
    cancellationType
    cancellationReason
    refundAmount
    deletedAt
    deletedBy
    
    # Subscription package info
    subscriptionPackage {
      id
      title
      amount
    }
  }
}

# 3. Query only active (non-cancelled) subscriptions
query GetActiveSubscriptions {
  companySubscriptions(filter: { deletedAt: { is: null } }) {
    id
    seatCount
    isYearly
    subscriptionEndDate
    nextBillingDate
    isActive
    nextBillingAmount
    
    subscriptionPackage {
      id
      title
      amount
    }
  }
}

# 4. Query only cancelled subscriptions
query GetCancelledSubscriptions {
  companySubscriptions(filter: { deletedAt: { isNot: null } }) {
    id
    seatCount
    isYearly
    subscriptionEndDate
    isCancelled
    cancellationType
    cancellationReason
    refundAmount
    deletedAt
    deletedBy
    
    subscriptionPackage {
      id
      title
      amount
    }
  }
}

# 5. Bulk cancel multiple subscriptions (only company owners)
mutation BulkCancelSubscriptions($ids: [ID!]!) {
  deleteManyCompanySubscriptions(input: { filter: { id: { in: $ids } } }) {
    deletedCount
  }
}

# Variables for bulk cancellation:
# {
#   "ids": ["123", "124", "125"]
# }

# Error Examples:
# If a non-owner tries to cancel a subscription, they will get:
# {
#   "errors": [
#     {
#       "message": "Only company owners can cancel subscriptions",
#       "extensions": {
#         "code": "UNAUTHORIZED"
#       }
#     }
#   ]
# }

# Authorization Rules:
# - Only company owners can delete/cancel subscriptions
# - Users can view their company's subscriptions (including cancelled ones)
# - Admins can delete any subscription
# - Soft deletion is used (deletedAt field) for audit trail
