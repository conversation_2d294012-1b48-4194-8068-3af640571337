import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { CompanySubscriptionEntity } from './entity/company-subscription.entity';
import {
  CompanySubscriptionDto,
  CreateCompanySubscriptionInputDTO,
  UpdateCompanySubscriptionInputDTO
} from './dto/company-subscription.gql.dto';
import { CompanySubscriptionService } from './company-subscription.service';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { CompanySubscriptionResolver } from './company-subscription.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([CompanySubscriptionEntity, SubscriptionPackageEntity])],
      resolvers: [
        {
          DTOClass: CompanySubscriptionDto,
          EntityClass: CompanySubscriptionEntity,
          CreateDTOClass: CreateCompanySubscriptionInputDTO,
          UpdateDTOClass: UpdateCompanySubscriptionInputDTO,
          create: {
            disabled: true
          },
          update: {
            disabled: true
          },
          delete: {
            // only company owner can delete company subscription
          },
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [CompanySubscriptionService]
    })
  ],
  providers: [CompanySubscriptionResolver],
})
export class CompanySubscriptionModule {}
