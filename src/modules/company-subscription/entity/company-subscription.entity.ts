import { BaseEntity } from '@modules/base/base';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { SubscriptionStatus, CancellationType } from '@constants';

@ObjectType()
@Entity('company_subscriptions')
export class CompanySubscriptionEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  subscriptionPackageId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  companyId: number;

  @FilterableField(() => GraphQLISODateTime)
  @Column('datetime')
  subscriptionEndDate: Date;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  paymentMethod: string;

  @FilterableField()
  @Column('int', { default: 1 })
  seatCount: number;

  @FilterableField()
  @Column('boolean', { default: false })
  isYearly: boolean;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  nextBillingDate: Date;

  @FilterableField()
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  creditBalance: number;

  @FilterableField(() => SubscriptionStatus)
  @Column('enum', { enum: SubscriptionStatus, default: SubscriptionStatus.Active })
  status: SubscriptionStatus;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  cancelledAt: Date;

  @FilterableField(() => CancellationType, { nullable: true })
  @Column('enum', { enum: CancellationType, nullable: true })
  cancellationType: CancellationType;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  cancellationReason: string;

  @FilterableField()
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  refundAmount: number;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  effectiveCancellationDate: Date;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => SubscriptionPackageEntity, subscriptionPackage => subscriptionPackage.companySubscriptions)
  @JoinColumn({ name: 'subscriptionPackageId' })
  subscriptionPackage: SubscriptionPackageEntity;

  @ManyToOne(() => CompanyEntity, company => company.companySubscriptions)
  @JoinColumn({ name: 'companyId' })
  company: CompanyEntity;
}
