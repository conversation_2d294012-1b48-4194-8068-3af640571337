import { Injectable, Logger } from '@nestjs/common';
import { Resolver, ResolveField, Parent } from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompanySubscriptionDto } from './dto/company-subscription.gql.dto';
import { CompanySubscriptionEntity } from './entity/company-subscription.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { calculateSubscriptionPrice } from '@common/pricing-helper';

@Injectable()
@Resolver(() => CompanySubscriptionDto)
export class CompanySubscriptionResolver {
  private readonly logger = new Logger(CompanySubscriptionResolver.name);

  constructor(
    @InjectRepository(CompanySubscriptionEntity)
    private readonly companySubscriptionRepo: Repository<CompanySubscriptionEntity>,
    @InjectRepository(SubscriptionPackageEntity)
    private readonly subscriptionPackageRepo: Repository<SubscriptionPackageEntity>
  ) {}

  /**
   * Custom resolver for nextBillingAmount that ensures subscriptionPackage is loaded
   * This resolver will be called whenever nextBillingAmount is requested in a GraphQL query
   */
  @ResolveField('nextBillingAmount', () => Number, { nullable: true })
  async nextBillingAmount(@Parent() subscription: CompanySubscriptionDto): Promise<number | null> {
    try {
      const isActive = subscription.subscriptionEndDate > new Date();
      if (!isActive) {
        this.logger.debug(`Subscription ${subscription.id} is not active`);
        return null;
      }

      // Load subscription package if not already loaded
      let subscriptionPackage = subscription.subscriptionPackage;
      if (!subscriptionPackage) {
        subscriptionPackage = await this.subscriptionPackageRepo.findOne({
          where: { id: subscription.subscriptionPackageId }
        });

        if (!subscriptionPackage) {
          this.logger.error(`Subscription package not found for subscription ${subscription.id}`);
          return null;
        }
      }

      // Calculate the next billing amount using the pricing helper
      const pricingResult = calculateSubscriptionPrice(
        subscriptionPackage,
        subscription.seatCount || 1,
        subscription.isYearly || false
      );

      // Use the full month period for renewal billing
      const totalAmount = pricingResult.fullMonthPeriod.totalAmount;

      // Apply credit balance
      const creditBalance = subscription.creditBalance || 0;
      const finalAmount = Math.max(0, totalAmount - creditBalance);

      return finalAmount;

    } catch (error) {
      this.logger.error(`Error calculating nextBillingAmount for subscription ${subscription.id}:`, error);
      return null;
    }
  }
}
