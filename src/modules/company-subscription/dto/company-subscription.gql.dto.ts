import { defaultQueryOptions } from '@constants';
import { Authorize, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { CompanySubscriptionEntity } from '../entity/company-subscription.entity';
import { SubscriptionPackageDto } from '@modules/subscription-package/dto/subscription-package.gql.dto';
import { relationOption } from '@constants/query.constant';
import { CompanySubscriptionAuthorizer } from '../company-subscription.authorizer';
import moment from 'moment';

@ObjectType('CompanySubscription')
@Authorize(CompanySubscriptionAuthorizer)
@Relation('subscriptionPackage', () => SubscriptionPackageDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class CompanySubscriptionDto extends CompanySubscriptionEntity {
  @Field(() => Boolean)
  get isSubscriptionActive() {
    return this.subscriptionEndDate > new Date();
    // return this.subscriptionEndDate > moment().subtract(1, "days").toDate()
  }

  // 10 DAYS GRACE PERIOD AFTER END OF SUBSCRIPTION PERIOD
  @Field(() => Boolean)
  get isSubscriptionInGracePeriod() {
    return new Date() > this.subscriptionEndDate && this.subscriptionEndDate > moment().subtract(10, "days").toDate();
  }

  @Field(() => Boolean, { nullable: true })
  isFreeTrial?: boolean;

  @Field(() => Boolean)
  get isTrialEnding() {
    if (!this.isFreeTrial) return false;
    const daysLeft = moment(this.subscriptionEndDate).diff(moment(), 'days');
    return daysLeft <= 3 && daysLeft >= 0;
  }

  @Field(() => Number, { nullable: true })
  get daysLeftInTrial() {
    if (!this.isFreeTrial) return null;
    return Math.max(0, moment(this.subscriptionEndDate).diff(moment(), 'days'));
  }

  @Field(() => Date, { nullable: true })
  get nextPaymentDate() {
    return this.nextBillingDate || this.subscriptionEndDate;
  }

  // nextBillingAmount is now handled by a custom resolver in company-subscription.resolver.ts
  // This ensures the subscriptionPackage relation is always loaded when needed
}

@InputType()
export class CreateCompanySubscriptionInputDTO {
  @IDField(() => ID) subscriptionPackageId: number;
  @IDField(() => ID) companyId: number;
  subscriptionEndDate: Date;
  seatCount?: number;
  isYearly?: boolean;
  nextBillingDate?: Date;
}

@InputType()
export class UpdateCompanySubscriptionInputDTO extends PartialType(CreateCompanySubscriptionInputDTO) {}

@InputType()
export class ChangeSeatCountInputDTO {
  @IDField(() => ID) subscriptionId: number;
  newSeatCount: number;
}
