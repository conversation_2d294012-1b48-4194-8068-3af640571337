import { defaultQueryOptions } from '@constants';
import { Authorize, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { CompanySubscriptionEntity } from '../entity/company-subscription.entity';
import { SubscriptionPackageDto } from '@modules/subscription-package/dto/subscription-package.gql.dto';
import { relationOption } from '@constants/query.constant';
import { CompanySubscriptionAuthorizer } from '../company-subscription.authorizer';
import { calculateSubscriptionPrice } from '@common/pricing-helper';
import moment from 'moment';

@ObjectType('CompanySubscription')
@Authorize(CompanySubscriptionAuthorizer)
@Relation('subscriptionPackage', () => SubscriptionPackageDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class CompanySubscriptionDto extends CompanySubscriptionEntity {
  @Field(() => Boolean)
  get isSubscriptionActive() {
    return this.subscriptionEndDate > new Date();
    // return this.subscriptionEndDate > moment().subtract(1, "days").toDate()
  }

  // 10 DAYS GRACE PERIOD AFTER END OF SUBSCRIPTION PERIOD
  @Field(() => Boolean)
  get isSubscriptionInGracePeriod() {
    return new Date() > this.subscriptionEndDate && this.subscriptionEndDate > moment().subtract(10, "days").toDate();
  }

  @Field(() => Boolean, { nullable: true })
  isFreeTrial?: boolean;

  @Field(() => Boolean)
  get isTrialEnding() {
    if (!this.isFreeTrial) return false;
    const daysLeft = moment(this.subscriptionEndDate).diff(moment(), 'days');
    return daysLeft <= 3 && daysLeft >= 0;
  }

  @Field(() => Number, { nullable: true })
  get daysLeftInTrial() {
    if (!this.isFreeTrial) return null;
    return Math.max(0, moment(this.subscriptionEndDate).diff(moment(), 'days'));
  }

  @Field(() => Date, { nullable: true })
  get nextPaymentDate() {
    return this.nextBillingDate || this.subscriptionEndDate;
  }

  /**
   * Calculate the next billing amount for this subscription
   * Takes into account seat count, billing period, and credit balance
   */
  @Field(() => Number, {
    nullable: true,
    description: 'The amount to be charged on the next billing cycle after applying credits'
  })
  get nextBillingAmount(): number | null {
    try {
      // Only calculate for active subscriptions
      if (!this.isSubscriptionActive || !this.subscriptionPackage) {
        return null;
      }

      // Calculate the next billing amount using the pricing helper
      const pricingResult = calculateSubscriptionPrice(
        this.subscriptionPackage,
        this.seatCount || 1,
        this.isYearly || false
      );

      // Use the full month period for renewal billing
      // The pricing helper already handles yearly vs monthly billing correctly:
      // - For monthly: returns the monthly amount
      // - For yearly: returns the full yearly amount (discounted monthly × 12)
      const totalAmount = pricingResult.fullMonthPeriod.totalAmount;

      // Apply credit balance
      const creditBalance = this.creditBalance || 0;
      const finalAmount = Math.max(0, totalAmount - creditBalance);

      return finalAmount;
    } catch (error) {
      // If calculation fails, return null
      console.error('Error calculating next billing amount:', error);
      return null;
    }
  }
}

@InputType()
export class CreateCompanySubscriptionInputDTO {
  @IDField(() => ID) subscriptionPackageId: number;
  @IDField(() => ID) companyId: number;
  subscriptionEndDate: Date;
  seatCount?: number;
  isYearly?: boolean;
  nextBillingDate?: Date;
}

@InputType()
export class UpdateCompanySubscriptionInputDTO extends PartialType(CreateCompanySubscriptionInputDTO) {}

@InputType()
export class ChangeSeatCountInputDTO {
  @IDField(() => ID) subscriptionId: number;
  newSeatCount: number;
}
