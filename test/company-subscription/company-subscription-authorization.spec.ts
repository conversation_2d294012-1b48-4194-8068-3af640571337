import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException } from '@nestjs/common';
import { CompanySubscriptionAuthorizer } from '../../src/modules/company-subscription/company-subscription.authorizer';
import { getRepository } from 'typeorm';
import { UserEntity } from '../../src/modules/user/entity/user.entity';
import { CompanyEntity } from '../../src/modules/company/entity/company.entity';
import { RoleTypeEnum } from '../../src/constants';

// Mock TypeORM
jest.mock('typeorm', () => ({
  getRepository: jest.fn(),
}));

describe('CompanySubscriptionAuthorizer', () => {
  let authorizer: CompanySubscriptionAuthorizer;
  let mockUserRepository: any;
  let mockCompanyRepository: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CompanySubscriptionAuthorizer],
    }).compile();

    authorizer = module.get<CompanySubscriptionAuthorizer>(CompanySubscriptionAuthorizer);

    // Setup mocks
    mockUserRepository = {
      findOne: jest.fn(),
    };
    mockCompanyRepository = {
      findOne: jest.fn(),
    };

    (getRepository as jest.Mock).mockImplementation((entity) => {
      if (entity === UserEntity) return mockUserRepository;
      if (entity === CompanyEntity) return mockCompanyRepository;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('authorize - delete operations', () => {
    it('should allow company owner to delete subscriptions', async () => {
      const userId = 1;
      const companyId = 100;
      const ownerId = 1; // Same as userId

      const mockUser = { id: userId, companyId };
      const mockCompany = { id: companyId, ownerId };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockCompanyRepository.findOne.mockResolvedValue(mockCompany);

      const context = {
        req: { user: { id: userId, type: RoleTypeEnum.User } },
        operationName: 'deleteOne'
      };

      const result = await authorizer.authorize(context);

      expect(result).toEqual({
        companyId: { eq: companyId }
      });
      expect(mockCompanyRepository.findOne).toHaveBeenCalledWith({
        where: { id: companyId }
      });
    });

    it('should deny non-owner users from deleting subscriptions', async () => {
      const userId = 1;
      const companyId = 100;
      const ownerId = 2; // Different from userId

      const mockUser = { id: userId, companyId };
      const mockCompany = { id: companyId, ownerId };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockCompanyRepository.findOne.mockResolvedValue(mockCompany);

      const context = {
        req: { user: { id: userId, type: RoleTypeEnum.User } },
        operationName: 'deleteOne'
      };

      await expect(authorizer.authorize(context)).rejects.toThrow(
        'Only company owners can cancel subscriptions'
      );
    });

    it('should allow admin to delete any subscription', async () => {
      const context = {
        req: { user: { id: 1, type: RoleTypeEnum.Admin } },
        operationName: 'deleteOne'
      };

      const result = await authorizer.authorize(context);

      expect(result).toEqual({});
      expect(mockUserRepository.findOne).not.toHaveBeenCalled();
      expect(mockCompanyRepository.findOne).not.toHaveBeenCalled();
    });
  });

  describe('authorize - non-delete operations', () => {
    it('should allow users to view their company subscriptions', async () => {
      const userId = 1;
      const companyId = 100;

      const mockUser = { id: userId, companyId };
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const context = {
        req: { user: { id: userId, type: RoleTypeEnum.User } },
        operationName: 'findMany'
      };

      const result = await authorizer.authorize(context);

      expect(result).toHaveProperty('companyId');
      expect(result).toHaveProperty('subscriptionEndDate');
      expect(mockCompanyRepository.findOne).not.toHaveBeenCalled(); // Should not check ownership for non-delete ops
    });
  });
});
